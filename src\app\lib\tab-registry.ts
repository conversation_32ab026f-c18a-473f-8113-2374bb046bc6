import type { ComponentType } from "react";
import dynamic from "next/dynamic";

// Các key của object này PHẢI KHỚP với các 'path' trong menu-registry.ts
export const tabRegistry: Record<string, ComponentType<any>> = {
  // --- DANH MỤC - Chỉ các item không có subItems ---
  "/danh-muc/dan-toc": dynamic(
    () => import("@/app/(dashboard)/danh-muc/dan-toc/page")
  ),
  "/danh-muc/tinh-thanh": dynamic(
    () => import("@/app/(dashboard)/danh-muc/tinh-thanh/page")
  ),
  "/danh-muc/quan-huyen": dynamic(
    () => import("@/app/(dashboard)/danh-muc/quan-huyen/page")
  ),
  "/danh-muc/xa-phuong": dynamic(
    () => import("@/app/(dashboard)/danh-muc/xa-phuong/page")
  ),

  // // --- CA LÀM VIỆC SUB ITEMS ---
  // "/danh-muc/ca-lam-viec/quan-ly-ca": dynamic(
  //   () => import("@/app/(dashboard)/danh-muc/ca-lam-viec/quan-ly-ca/page")
  // ),
  // "/danh-muc/ca-lam-viec/phan-cong-ca": dynamic(
  //   () => import("@/app/(dashboard)/danh-muc/ca-lam-viec/phan-cong-ca/page")
  // ),
  // "/danh-muc/ca-lam-viec/thong-ke-ca": dynamic(
  //   () => import("@/app/(dashboard)/danh-muc/ca-lam-viec/thong-ke-ca/page")
  // ),

  // // --- GIẤY CHỨNG NHẬN SUB ITEMS ---
  // "/danh-muc/giay-chung-nhan/tay-nghe": dynamic(
  //   () => import("@/app/(dashboard)/danh-muc/giay-chung-nhan/tay-nghe/page")
  // ),
  // "/danh-muc/giay-chung-nhan/an-toan": dynamic(
  //   () => import("@/app/(dashboard)/danh-muc/giay-chung-nhan/an-toan/page")
  // ),

  // // --- NHÂN SỰ - Chỉ item không có subItems ---
  // "/diem-danh-hang-ngay": dynamic(
  //   () => import("@/app/(dashboard)/nhan-su/diem-danh-hang-ngay/page")
  // ),

  // // --- QUẢN LÝ NHÂN SỰ SUB ITEMS ---
  // "/quan-ly-nhan-su/tuyen-dung": dynamic(
  //   () => import("@/app/(dashboard)/nhan-su/quan-ly-nhan-su/tuyen-dung/page")
  // ),
  // "/quan-ly-nhan-su/nghi-viec": dynamic(
  //   () => import("@/app/(dashboard)/nhan-su/quan-ly-nhan-su/nghi-viec/page")
  // ),
  // "/quan-ly-nhan-su/ho-so": dynamic(
  //   () => import("@/app/(dashboard)/nhan-su/quan-ly-nhan-su/ho-so/page")
  // ),

  // // --- HỢP ĐỒNG LAO ĐỘNG SUB ITEMS ---
  // "/hop-dong-lao-dong/hop-dong-moi": dynamic(
  //   () => import("@/app/(dashboard)/nhan-su/hop-dong-lao-dong/hop-dong-moi/page")
  // ),
  // "/hop-dong-lao-dong/gia-han": dynamic(
  //   () => import("@/app/(dashboard)/nhan-su/hop-dong-lao-dong/gia-han/page")
  // ),
};

// Hàm helper không thay đổi
export const getComponentForTab = (path: string): ComponentType<any> | null => {
  return tabRegistry[path] || null;
};
