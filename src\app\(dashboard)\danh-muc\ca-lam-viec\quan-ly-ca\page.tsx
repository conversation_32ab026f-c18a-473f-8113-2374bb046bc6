"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Clock, Users, Calendar, Timer } from "lucide-react";

export default function QuanLyCaPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Quản Lý Ca Làm Việc
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Quản lý thông tin các ca làm việc trong công ty
          </p>
        </div>
        <Button onClick={handleAddShift} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Thêm Ca Mới
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Tổng Ca
                </p>
                <p className="text-xl font-semibold">{shifts.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <Timer className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Ca Hoạt Động
                </p>
                <p className="text-xl font-semibold">
                  {shifts.filter((s) => s.status === "active").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                <Users className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Nhân Viên
                </p>
                <p className="text-xl font-semibold">55</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                <Calendar className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Giờ Làm/Ngày
                </p>
                <p className="text-xl font-semibold">7.5</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Content */}
      <Card>
        <CardHeader>
          <CardTitle>Danh Sách Ca Làm Việc</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold">Ca Sáng</h3>
              <p className="text-sm text-gray-600">08:00 - 12:00 (4 giờ)</p>
              <p className="text-sm text-gray-500">
                25 nhân viên được phân công
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold">Ca Chiều</h3>
              <p className="text-sm text-gray-600">13:00 - 17:00 (4 giờ)</p>
              <p className="text-sm text-gray-500">
                30 nhân viên được phân công
              </p>
            </div>
            <div className="p-4 border rounded-lg bg-gray-50">
              <h3 className="font-semibold text-gray-500">Ca Tối</h3>
              <p className="text-sm text-gray-500">18:00 - 22:00 (4 giờ)</p>
              <p className="text-sm text-gray-400">Tạm dừng hoạt động</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
