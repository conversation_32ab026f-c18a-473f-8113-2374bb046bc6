"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Calendar,
  Plus,
  Search,
  Edit,
  Trash2,
  Users,
  Clock,
  UserCheck,
} from "lucide-react";

interface ShiftAssignment {
  id: string;
  employeeName: string;
  employeeId: string;
  shiftName: string;
  date: string;
  status: "assigned" | "completed" | "absent";
  department: string;
}

export default function PhanCongCaPage() {
  const [assignments, setAssignments] = useState<ShiftAssignment[]>([
    {
      id: "1",
      employeeName: "<PERSON>uyễn <PERSON>n <PERSON>",
      employeeId: "NV001",
      shiftName: "Ca Sáng",
      date: "2024-01-15",
      status: "assigned",
      department: "Kỹ thuật",
    },
    {
      id: "2",
      employeeName: "Trần Thị B",
      employeeId: "NV002",
      shiftName: "Ca Chiều",
      date: "2024-01-15",
      status: "completed",
      department: "Hành chính",
    },
    {
      id: "3",
      employeeName: "Lê Văn C",
      employeeId: "NV003",
      shiftName: "Ca Tối",
      date: "2024-01-15",
      status: "absent",
      department: "Kỹ thuật",
    },
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDate, setSelectedDate] = useState("2024-01-15");
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const filteredAssignments = assignments.filter(
    (assignment) =>
      assignment.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) &&
      assignment.date === selectedDate
  );

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "assigned":
        return <Badge variant="default">Đã phân công</Badge>;
      case "completed":
        return <Badge variant="secondary">Hoàn thành</Badge>;
      case "absent":
        return <Badge variant="destructive">Vắng mặt</Badge>;
      default:
        return <Badge variant="outline">Không xác định</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Phân Công Ca Làm Việc
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Quản lý phân công ca làm việc cho nhân viên
          </p>
        </div>
        <Button onClick={() => setIsDialogOpen(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Phân Công Mới
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Tổng Phân Công
                </p>
                <p className="text-xl font-semibold">{assignments.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <UserCheck className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Hoàn Thành
                </p>
                <p className="text-xl font-semibold">
                  {assignments.filter((a) => a.status === "completed").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
                <Clock className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Đang Thực Hiện
                </p>
                <p className="text-xl font-semibold">
                  {assignments.filter((a) => a.status === "assigned").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
                <Calendar className="h-5 w-5 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Vắng Mặt
                </p>
                <p className="text-xl font-semibold">
                  {assignments.filter((a) => a.status === "absent").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Tìm kiếm nhân viên..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="w-48">
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Assignments Table */}
      <Card>
        <CardHeader>
          <CardTitle>Danh Sách Phân Công Ca</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Mã NV</TableHead>
                <TableHead>Tên Nhân Viên</TableHead>
                <TableHead>Phòng Ban</TableHead>
                <TableHead>Ca Làm Việc</TableHead>
                <TableHead>Ngày</TableHead>
                <TableHead>Trạng Thái</TableHead>
                <TableHead>Thao Tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAssignments.map((assignment) => (
                <TableRow key={assignment.id}>
                  <TableCell className="font-medium">
                    {assignment.employeeId}
                  </TableCell>
                  <TableCell>{assignment.employeeName}</TableCell>
                  <TableCell>{assignment.department}</TableCell>
                  <TableCell>{assignment.shiftName}</TableCell>
                  <TableCell>{assignment.date}</TableCell>
                  <TableCell>{getStatusBadge(assignment.status)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add Assignment Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Phân Công Ca Mới</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="employee">Nhân Viên</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn nhân viên" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="nv001">Nguyễn Văn A</SelectItem>
                  <SelectItem value="nv002">Trần Thị B</SelectItem>
                  <SelectItem value="nv003">Lê Văn C</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="shift">Ca Làm Việc</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn ca làm việc" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="morning">Ca Sáng</SelectItem>
                  <SelectItem value="afternoon">Ca Chiều</SelectItem>
                  <SelectItem value="evening">Ca Tối</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="date">Ngày Làm Việc</Label>
              <Input id="date" type="date" />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Hủy
              </Button>
              <Button onClick={() => setIsDialogOpen(false)}>
                Phân Công
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
