import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Clock, Edit, Trash2 } from "lucide-react";

export default function CaLamViecPage() {
  const shifts = [
    {
      id: 1,
      name: "Ca Sáng",
      time: "08:00 - 12:00",
      status: "active",
      employees: 45,
    },
    {
      id: 2,
      name: "<PERSON><PERSON>ề<PERSON>",
      time: "13:00 - 17:00",
      status: "active",
      employees: 38,
    },
    {
      id: 3,
      name: "<PERSON><PERSON> Tố<PERSON>",
      time: "18:00 - 22:00",
      status: "active",
      employees: 22,
    },
    {
      id: 4,
      name: "<PERSON><PERSON> Đêm",
      time: "22:00 - 06:00",
      status: "inactive",
      employees: 15,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold"><PERSON><PERSON><PERSON><PERSON> lý <PERSON>a Làm <PERSON></h1>
          <p className="text-muted-foreground">
            <PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON> và quản lý các ca làm việc trong công ty
          </p>
        </div>
        <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
          <Plus className="h-4 w-4 mr-2" />
          Thêm ca mới
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">
                  Tổng ca làm việc
                </p>
                <p className="text-2xl font-bold">4</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">
                  Ca đang hoạt động
                </p>
                <p className="text-2xl font-bold">3</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-muted-foreground">Tổng nhân viên</p>
                <p className="text-2xl font-bold">120</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-muted-foreground">Trung bình/ca</p>
                <p className="text-2xl font-bold">30</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Shifts List */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {shifts.map((shift) => (
          <Card
            key={shift.id}
            className="hover:shadow-lg transition-shadow duration-200"
          >
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-blue-600" />
                  <CardTitle>{shift.name}</CardTitle>
                </div>
                <Badge
                  variant={shift.status === "active" ? "default" : "secondary"}
                >
                  {shift.status === "active" ? "Hoạt động" : "Tạm dừng"}
                </Badge>
              </div>
              <CardDescription>Thời gian: {shift.time}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Số nhân viên</p>
                  <p className="text-lg font-semibold">
                    {shift.employees} người
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
