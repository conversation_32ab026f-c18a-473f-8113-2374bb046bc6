"use client";

import { Separator } from "@/components/ui/separator";
import { Clock, Wifi, User, MapPin } from "lucide-react";
import { CurrentTimeClient } from "./CurrentTimeClient";

interface FooterProps {
  activeSection: string;
}

export function Footer({ activeSection }: FooterProps) {
  return (
    <footer className="h-8 bg-gradient-to-r from-slate-100 to-slate-50 dark:from-slate-900 dark:to-slate-800 border-t border-slate-200 dark:border-slate-700 px-4 flex items-center justify-between text-xs text-slate-600 dark:text-slate-400">
      {/* Left Section */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-1">
          <User className="h-3 w-3" />
          <span>Current Section: {activeSection || "N/A"}</span>
        </div>
        <Separator orientation="vertical" className="h-4" />
        <div className="flex items-center gap-1">
          <MapPin className="h-3 w-3" />
          <span>IP: *************</span>
        </div>
      </div>

      {/* Right Section */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          <span>Login: 9:40:56 AM</span>
        </div>
        <Separator orientation="vertical" className="h-4" />
        <div className="flex items-center gap-1">
          <Wifi className="h-3 w-3 text-green-500" />
          <span>Session: 00:00:09</span>
        </div>
        <Separator orientation="vertical" className="h-4" />
        <CurrentTimeClient />
      </div>
    </footer>
  );
}
