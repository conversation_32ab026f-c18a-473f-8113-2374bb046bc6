"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
} from "recharts";
import {
  TrendingUp,
  Calendar,
  Clock,
  Users,
  Download,
  Filter,
} from "lucide-react";

interface ShiftStats {
  shiftName: string;
  totalHours: number;
  employeeCount: number;
  completionRate: number;
  absenceRate: number;
}

const shiftData = [
  { name: "<PERSON><PERSON>án<PERSON>", hours: 120, employees: 25, completion: 95 },
  { name: "<PERSON><PERSON>ề<PERSON>", hours: 135, employees: 30, completion: 88 },
  { name: "<PERSON><PERSON> Tối", hours: 80, employees: 20, completion: 92 },
];

const pieData = [
  { name: "Hoàn thành", value: 275, color: "#10b981" },
  { name: "Vắng mặt", value: 25, color: "#ef4444" },
  { name: "Muộn", value: 15, color: "#f59e0b" },
];

export default function ThongKeCaPage() {
  const [selectedPeriod, setSelectedPeriod] = useState("week");
  const [selectedShift, setSelectedShift] = useState("all");

  const stats: ShiftStats[] = [
    {
      shiftName: "Ca Sáng",
      totalHours: 120,
      employeeCount: 25,
      completionRate: 95,
      absenceRate: 5,
    },
    {
      shiftName: "Ca Chiều",
      totalHours: 135,
      employeeCount: 30,
      completionRate: 88,
      absenceRate: 12,
    },
    {
      shiftName: "Ca Tối",
      totalHours: 80,
      employeeCount: 20,
      completionRate: 92,
      absenceRate: 8,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Thống Kê Ca Làm Việc
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Báo cáo và thống kê hiệu suất ca làm việc
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Bộ Lọc
          </Button>
          <Button className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Xuất Báo Cáo
          </Button>
        </div>
      </div>

      {/* Filter Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="w-48">
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn kỳ báo cáo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">Tuần này</SelectItem>
                  <SelectItem value="month">Tháng này</SelectItem>
                  <SelectItem value="quarter">Quý này</SelectItem>
                  <SelectItem value="year">Năm này</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="w-48">
              <Select value={selectedShift} onValueChange={setSelectedShift}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn ca làm việc" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả ca</SelectItem>
                  <SelectItem value="morning">Ca Sáng</SelectItem>
                  <SelectItem value="afternoon">Ca Chiều</SelectItem>
                  <SelectItem value="evening">Ca Tối</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1">
              <Input type="date" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Tổng Giờ Làm
                </p>
                <p className="text-xl font-semibold">335 giờ</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <Users className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Nhân Viên Tham Gia
                </p>
                <p className="text-xl font-semibold">75 người</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                <TrendingUp className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Tỷ Lệ Hoàn Thành
                </p>
                <p className="text-xl font-semibold">91.7%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                <Calendar className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Tỷ Lệ Vắng Mặt
                </p>
                <p className="text-xl font-semibold">8.3%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Bar Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Thống Kê Theo Ca</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={shiftData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="hours" fill="#3b82f6" name="Giờ làm" />
                <Bar dataKey="employees" fill="#10b981" name="Nhân viên" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Tỷ Lệ Chấm Công</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) =>
                    `${name} ${(percent * 100).toFixed(0)}%`
                  }
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Stats Table */}
      <Card>
        <CardHeader>
          <CardTitle>Chi Tiết Thống Kê Theo Ca</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Ca Làm Việc</TableHead>
                <TableHead>Tổng Giờ</TableHead>
                <TableHead>Số Nhân Viên</TableHead>
                <TableHead>Tỷ Lệ Hoàn Thành</TableHead>
                <TableHead>Tỷ Lệ Vắng Mặt</TableHead>
                <TableHead>Hiệu Suất</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {stats.map((stat) => (
                <TableRow key={stat.shiftName}>
                  <TableCell className="font-medium">{stat.shiftName}</TableCell>
                  <TableCell>{stat.totalHours} giờ</TableCell>
                  <TableCell>{stat.employeeCount} người</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        stat.completionRate >= 90 ? "default" : "secondary"
                      }
                    >
                      {stat.completionRate}%
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={stat.absenceRate <= 10 ? "default" : "destructive"}
                    >
                      {stat.absenceRate}%
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        stat.completionRate >= 90 && stat.absenceRate <= 10
                          ? "default"
                          : "secondary"
                      }
                    >
                      {stat.completionRate >= 90 && stat.absenceRate <= 10
                        ? "Tốt"
                        : "Cần cải thiện"}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
