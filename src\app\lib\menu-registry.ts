// QUAN TRỌNG: Bạn cần định nghĩa hoặc import `navigationItems` vào đây.
// Lựa chọn 1: Import từ file navigation (Khuyến khích)
import { navigationItems } from "./navigationMenu";

// Định nghĩa kiểu dữ liệu chi tiết cho một mục menu
export interface MenuItemDetail {
  code: string;
  path: string;
  title: string;
}

// Hàm đệ quy này là hàm nội bộ, không cần export
function flattenMenuItems(items: any[], pathPrefix = ""): MenuItemDetail[] {
  let flatList: MenuItemDetail[] = [];

  for (const item of items) {
    const currentPath = `${pathPrefix}${
      item.path || "/" + item.code.toLowerCase()
    }`;

    // Chỉ thêm các mục cuối cùng (có thể click)
    if (!item.items && !item.subItems) {
      flatList.push({
        code: item.code,
        path: currentPath,
        title: item.name,
      });
    }

    // Xử lý items (cấp 2)
    if (item.items) {
      flatList = [
        ...flatList,
        ...flattenMenuItems(item.items, item.pathPrefix || currentPath),
      ];
    }

    // Xử lý subItems (cấp 3)
    if (item.subItems) {
      flatList = [...flatList, ...flattenMenuItems(item.subItems, currentPath)];
    }
  }
  return flatList;
}

// Tạo một danh sách phẳng chứa tất cả các mục menu có thể click
// THÊM `export`
export const allMenuItems: MenuItemDetail[] = flattenMenuItems(navigationItems);

// Tạo một Map để tra cứu nhanh bằng code hoặc path
const menuByCode = new Map(allMenuItems.map((item) => [item.code, item]));
const menuByPath = new Map(allMenuItems.map((item) => [item.path, item]));

// Hàm helper để lấy thông tin menu từ code
// THÊM `export`
export const getMenuItemByCode = (code: string): MenuItemDetail | undefined => {
  return menuByCode.get(code);
};

// Hàm helper để lấy thông tin menu từ path
// THÊM `export`
export const getMenuItemByPath = (path: string): MenuItemDetail | undefined => {
  return menuByPath.get(path);
};
