import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Plus, MapPin, Edit, Trash2, Search } from "lucide-react";
import { Input } from "@/components/ui/input";

export default function TinhThanhPage() {
  const provinces = [
    {
      id: 1,
      name: "Hà Nội",
      code: "HN",
      region: "Miền Bắc",
      districts: 30,
      status: "active",
    },
    {
      id: 2,
      name: "<PERSON><PERSON> Chí Minh",
      code: "HCM",
      region: "Miền Nam",
      districts: 24,
      status: "active",
    },
    {
      id: 3,
      name: "Đà Nẵng",
      code: "DN",
      region: "Miền Trung",
      districts: 8,
      status: "active",
    },
    {
      id: 4,
      name: "Hải Phòng",
      code: "HP",
      region: "Miền Bắc",
      districts: 15,
      status: "active",
    },
    {
      id: 5,
      name: "<PERSON><PERSON><PERSON>hơ",
      code: "CT",
      region: "Miền Nam",
      districts: 9,
      status: "active",
    },
    {
      id: 6,
      name: "An Giang",
      code: "AG",
      region: "Miền Nam",
      districts: 11,
      status: "inactive",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Quản lý Tỉnh Thành</h1>
          <p className="text-muted-foreground">
            Quản lý danh sách các tỉnh thành trong hệ thống
          </p>
        </div>
        <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
          <Plus className="h-4 w-4 mr-2" />
          Thêm tỉnh thành
        </Button>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Tìm kiếm tỉnh thành..." className="pl-10" />
            </div>
            <Button variant="outline">Lọc theo miền</Button>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Tổng tỉnh thành</p>
                <p className="text-2xl font-bold">63</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Đang hoạt động</p>
                <p className="text-2xl font-bold">58</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-muted-foreground">Miền Bắc</p>
                <p className="text-2xl font-bold">25</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-muted-foreground">Miền Nam</p>
                <p className="text-2xl font-bold">21</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Provinces List */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách Tỉnh Thành</CardTitle>
          <CardDescription>
            Quản lý thông tin các tỉnh thành trong hệ thống
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {provinces.map((province) => (
              <div
                key={province.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center gap-4">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <MapPin className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold">{province.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      Mã: {province.code} • {province.region} •{" "}
                      {province.districts} quận/huyện
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    variant={
                      province.status === "active" ? "default" : "secondary"
                    }
                  >
                    {province.status === "active" ? "Hoạt động" : "Tạm dừng"}
                  </Badge>
                  <Button variant="outline" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
