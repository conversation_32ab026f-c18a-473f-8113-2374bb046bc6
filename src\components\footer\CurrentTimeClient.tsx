"use client";

import { useEffect, useState } from "react";

export function CurrentTimeClient() {
  const [currentTime, setCurrentTime] = useState<Date | null>(null);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  if (!currentTime) return <span>Đang tải...</span>;

  return <span>{currentTime.toLocaleString("vi-VN")}</span>;
}
