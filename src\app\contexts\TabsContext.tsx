"use client";

import {
  createContext,
  useContext,
  useState,
  type ReactNode,
  useCallback,
} from "react";
import { useRouter } from "next/navigation";

// Define the structure of a tab
export interface Tab {
  id: string; // Used as unique ID, e.g., '/dashboard'
  title: string; // Display title on the tab
  path: string; // Path of the page
}

// Define what the context will provide
interface TabsContextType {
  tabs: Tab[];
  activeTabId: string | null;
  openTab: (tab: Tab) => void;
  closeTab: (tabId: string) => void;
  setActiveTabId: (tabId: string) => void;
  closeAllTabs: () => void;
}

// Create Context
const TabsContext = createContext<TabsContextType | undefined>(undefined);

// Create Provider component
export function TabsProvider({ children }: { children: ReactNode }) {
  const [tabs, setTabs] = useState<Tab[]>([]);
  const [activeTabId, setActiveTabIdState] = useState<string | null>(null);
  const router = useRouter(); // Used for URL updates

  const setActiveTabId = useCallback(
    (tabId: string) => {
      setActiveTabIdState(tabId);
      // Update URL without a full page reload
      router.push(tabId, { scroll: false });
    },
    [router]
  );

  const openTab = useCallback(
    (newTab: Tab) => {
      // Check if tab already exists outside of setTabs callback
      const tabExists = tabs.find((tab) => tab.id === newTab.id);

      if (tabExists) {
        // If it exists, just activate it directly
        setActiveTabId(newTab.id);
      } else {
        // If not, add new tab and then activate it
        setTabs((prevTabs) => [...prevTabs, newTab]);
        // Call setActiveTabId AFTER setTabs, but still within the same event loop tick.
        // React batches these updates, so it should be fine.
        setActiveTabId(newTab.id);
      }
    },
    [tabs, setActiveTabId]
  ); // Dependency on tabs is crucial here

  const closeTab = useCallback(
    (tabIdToClose: string) => {
      let newActiveTabId: string | null = activeTabId;
      const tabIndex = tabs.findIndex((tab) => tab.id === tabIdToClose);

      // If the closed tab is the active tab, determine the new active tab
      if (tabIdToClose === activeTabId) {
        if (tabs.length > 1) {
          // Prioritize activating the adjacent tab (next or previous)
          const nextTab = tabs[tabIndex + 1] || tabs[tabIndex - 1];
          newActiveTabId = nextTab ? nextTab.id : null; // Ensure nextTab exists
        } else {
          newActiveTabId = null; // No more tabs left
        }
      }

      // Filter out the closed tab
      const newTabs = tabs.filter((tab) => tab.id !== tabIdToClose);
      setTabs(newTabs); // Update the tabs state first

      // Update active tab and potentially navigate AFTER tabs state is set
      if (newActiveTabId) {
        setActiveTabId(newActiveTabId);
      } else {
        // If no tabs left, navigate to the homepage
        router.push("/");
        setActiveTabIdState(null); // Also clear activeTabId in state
      }
    },
    [tabs, activeTabId, setActiveTabId, router]
  ); // Dependencies on tabs, activeTabId, setActiveTabId, router

  // New function to close all tabs
  const closeAllTabs = useCallback(() => {
    setTabs([]);
    setActiveTabIdState(null); // Clear activeTabId
    router.push("/"); // Navigate to the homepage
  }, [router]); // Dependency on router

  const value = {
    tabs,
    activeTabId,
    openTab,
    closeTab,
    setActiveTabId,
    closeAllTabs,
  };

  return <TabsContext.Provider value={value}>{children}</TabsContext.Provider>;
}

// Create custom hook to easily use the context
export function useTabs() {
  const context = useContext(TabsContext);
  if (context === undefined) {
    throw new Error("useTabs must be used within a TabsProvider");
  }
  return context;
}
