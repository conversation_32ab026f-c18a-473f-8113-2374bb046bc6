"use client";

import type React from "react";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";

import { Header } from "@/components/header/header";
import { Footer } from "@/components/footer/footer";
import { TabsProvider, useTabs } from "@/app/contexts/TabsContext";
import { TabContainer } from "@/components/tab-container";
import { getMenuItemByCode, getMenuItemByPath } from "@/app/lib/menu-registry";

// Component con này dùng để truy cập context sau khi Provider đã được thiết lập
function DashboardUI() {
  const { openTab, activeTabId } = useTabs();
  const pathname = usePathname();
  const [currentUserRole, setCurrentUserRole] = useState<"admin" | "user">(
    "admin"
  );

  // Hàm này kết nối Navigation với hệ thống Tab
  const handleSectionChange = (code: string) => {
    const menuItem = getMenuItemByCode(code);
    if (menuItem) {
      openTab({
        id: menuItem.path,
        path: menuItem.path,
        title: menuItem.title,
      });
    } else {
      console.warn(`Menu item with code "${code}" not found in registry.`);
    }
  };

  // Tìm 'code' của menu item tương ứng với tab đang active để highlight
  const activeSectionCode = activeTabId
    ? getMenuItemByPath(activeTabId)?.code
    : undefined;

  // Mở tab ban đầu dựa trên URL khi tải trang
  useEffect(() => {
    const menuItem = getMenuItemByPath(pathname);
    if (menuItem) {
      openTab({
        id: menuItem.path,
        path: menuItem.path,
        title: menuItem.title,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Chỉ chạy 1 lần

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-slate-900">
      <Header
        activeSection={activeSectionCode || ""}
        onSectionChange={handleSectionChange}
        userRole={currentUserRole}
      />

      {/* TabContainer sẽ thay thế cho MainContent cũ */}
      <main
        className="flex-grow flex flex-col overflow-hidden"
        style={{ height: "calc(100vh - 110px)" }}
      >
        <TabContainer />
      </main>

      <Footer activeSection={activeSectionCode || ""} />
    </div>
  );
}

// Layout chính cho route group (dashboard)
export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <TabsProvider>
      <DashboardUI />
    </TabsProvider>
  );
}
