"use client";

import Link from "next/link";
import { useState } from "react";
import { useTabs } from "@/app/contexts/TabsContext";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export function Logo() {
  const [isOpen, setIsOpen] = useState(false);
  const { tabs, closeAllTabs } = useTabs();

  const handleConfirmReturnHome = () => {
    closeAllTabs();
    setIsOpen(false);
  };

  return (
    <div className="flex items-center space-x-4">
      {tabs.length > 0 ? (
        <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
          <AlertDialogTrigger asChild>
            <Link
              href="/"
              className="relative block my-1 px-4 py-2 transition-all duration-500 rounded-sm group"
              onClick={(e) => {
                e.preventDefault();
                setIsOpen(true);
              }}
            >
              {/* Background với gradient hiện đại */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-sm opacity-0 group-hover:opacity-100 transition-all duration-300 blur-lg" />

              {/* Nền hover với glassmorphism - bỏ border */}
              <div className="absolute inset-0 bg-gradient-to-br from-green-50/80 via-emerald-50/60 to-teal-50/80 dark:from-blue-900/20 dark:via-purple-900/15 dark:to-cyan-900/20 rounded-sm opacity-0 group-hover:opacity-100 transition-all duration-300 backdrop-blur-sm" />

              {/* Nội dung Logo */}
              <div className="relative z-10 flex items-center space-x-3">
                {/* Text */}
                <div className="flex flex-col">
                  <span className="text-slate-800 dark:text-white font-bold text-xl tracking-wide transition-all duration-300 group-hover:text-green-600 dark:group-hover:text-blue-400">
                    HR Manager
                  </span>
                </div>

                {/* Animated dots */}
                <div className="flex flex-col space-y-1 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-x-2 group-hover:translate-x-0">
                  <div className="w-2 h-2 bg-blue-500 rounded-sm animate-pulse" />
                  <div
                    className="w-2 h-2 bg-purple-500 rounded-sm animate-pulse"
                    style={{ animationDelay: "0.3s" }}
                  />
                  <div
                    className="w-2 h-2 bg-cyan-500 rounded-sm animate-pulse"
                    style={{ animationDelay: "0.6s" }}
                  />
                </div>
              </div>

              {/* Corner accents */}
              <div className="absolute top-2 left-2 w-3 h-3 border-t-2 border-l-2 border-blue-500/60 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-tl" />
              <div className="absolute bottom-2 right-2 w-3 h-3 border-b-2 border-r-2 border-purple-500/60 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-br" />
            </Link>
          </AlertDialogTrigger>

          <AlertDialogContent className="sm:max-w-md">
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">HR</span>
                </div>
                Xác nhận trở về Trang chủ?
              </AlertDialogTitle>
              <AlertDialogDescription>
                Bạn có chắc chắn muốn trở về trang chủ? Toàn bộ{" "}
                <strong>{tabs.length} tab</strong> đang mở sẽ bị đóng.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Hủy</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleConfirmReturnHome}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                Xác nhận
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      ) : (
        <Link
          href="/"
          className="relative block my-1 px-4 py-2 transition-all duration-500 rounded-sm group"
        >
          {/* Background với gradient hiện đại */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-sm opacity-0 group-hover:opacity-100 transition-all duration-300 blur-lg" />

          {/* Nền hover với glassmorphism - bỏ border */}
          <div className="absolute inset-0 bg-gradient-to-br from-green-50/80 via-emerald-50/60 to-teal-50/80 dark:from-blue-900/20 dark:via-purple-900/15 dark:to-cyan-900/20 rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-300 backdrop-blur-sm" />

          {/* Nội dung Logo */}
          <div className="relative z-10 flex items-center space-x-3">
            {/* Text */}
            <div className="flex flex-col">
              <span className="text-slate-800 dark:text-white font-bold text-xl tracking-wide transition-all duration-300 group-hover:text-blue-600 dark:group-hover:text-blue-400">
                HR Manager
              </span>
            </div>

            {/* Animated dots */}
            <div className="flex flex-col space-y-1 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-x-2 group-hover:translate-x-0">
              <div className="w-2 h-2 bg-blue-500 rounded-sm animate-pulse" />
              <div
                className="w-2 h-2 bg-purple-500 rounded-sm animate-pulse"
                style={{ animationDelay: "0.3s" }}
              />
              <div
                className="w-2 h-2 bg-cyan-500 rounded-sm animate-pulse"
                style={{ animationDelay: "0.6s" }}
              />
            </div>
          </div>

          {/* Corner accents */}
          <div className="absolute top-2 left-2 w-3 h-3 border-t-2 border-l-2 border-blue-500/60 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-tl" />
          <div className="absolute bottom-2 right-2 w-3 h-3 border-b-2 border-r-2 border-purple-500/60 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-br" />
        </Link>
      )}
    </div>
  );
}
