import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
  Menu,
  ChevronDown,
  ChevronRight,
  Sparkles,
  ArrowRight,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useState } from "react";
import { navigationItems } from "@/app/lib/navigationMenu";
import { useTabs } from "@/app/contexts/TabsContext";
import { getMenuItemByPath } from "@/app/lib/menu-registry";

interface NavigationProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
  userRole?: "admin" | "user";
}

export function Navigation({
  activeSection,
  onSectionChange,
  userRole = "user",
}: NavigationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState<string[]>([]);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const { openTab } = useTabs();

  const toggleSection = (sectionCode: string) => {
    setExpandedSections((prev) =>
      prev.includes(sectionCode)
        ? prev.filter((code) => code !== sectionCode)
        : [...prev, sectionCode]
    );
  };

  const toggleItem = (itemCode: string) => {
    setExpandedItems((prev) =>
      prev.includes(itemCode)
        ? prev.filter((code) => code !== itemCode)
        : [...prev, itemCode]
    );
  };

  const hasAccess = (roles: string[]) => {
    return roles.includes(userRole);
  };

  const getFilteredItems = (items: any[]) => {
    return items.filter((item) => hasAccess(item.roles || ["admin", "user"]));
  };

  const getFilteredSubItems = (subItems: any[]) => {
    return subItems.filter((subItem) =>
      hasAccess(subItem.roles || ["admin", "user"])
    );
  };

  const handleItemClick = (item: any, section: any) => {
    // Nếu có subItems, toggle expand (chỉ cho mobile)
    if (item.subItems && getFilteredSubItems(item.subItems).length > 0) {
      toggleItem(item.code);
      return;
    }

    // Nếu không có subItems và có path, mở tab
    if (item.path) {
      const fullPath = `${section.pathPrefix || "/danh-muc"}${item.path}`;
      const menuItem = getMenuItemByPath(fullPath);

      if (menuItem) {
        openTab({
          id: fullPath,
          title: item.name,
          path: fullPath,
        });
      }

      onSectionChange(item.code);
      setIsOpen(false);
    }
  };

  const handleSubItemClick = (subItem: any, item: any, section: any) => {
    const fullPath = `${section.pathPrefix || "/danh-muc"}${subItem.path}`;
    const menuItem = getMenuItemByPath(fullPath);

    if (menuItem) {
      openTab({
        id: fullPath,
        title: subItem.name,
        path: fullPath,
      });
    }

    onSectionChange(subItem.code);
    setIsOpen(false);
  };

  const NavigationContent = () => (
    <div className="w-full bg-gradient-to-b from-slate-50 to-white dark:from-slate-900 dark:to-slate-950 min-h-full">
      {navigationItems
        .filter((section) => hasAccess(section.roles || ["admin", "user"]))
        .map((section) => {
          const Icon = section.icon;
          const isExpanded = expandedSections.includes(section.code);
          const filteredItems = getFilteredItems(section.items);

          if (filteredItems.length === 0) return null;

          return (
            <div
              key={section.code}
              className="border-b border-slate-200 dark:border-slate-800"
            >
              {/* Section Header */}
              <div
                className={cn(
                  "flex items-center justify-between px-4 py-3 cursor-pointer group",
                  "bg-gradient-to-r from-slate-100 to-slate-50 dark:from-slate-800 dark:to-slate-900",
                  "hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900/20 dark:hover:to-purple-900/20",
                  "transition-all duration-300"
                )}
                onClick={() => toggleSection(section.code)}
              >
                <div className="flex items-center gap-3">
                  <div
                    className={cn(
                      "p-2 rounded-lg transition-all duration-300",
                      "bg-white dark:bg-slate-800 shadow-sm",
                      "group-hover:bg-gradient-to-br group-hover:from-blue-500 group-hover:to-purple-600",
                      "group-hover:text-white"
                    )}
                  >
                    <Icon className="h-4 w-4" />
                  </div>
                  <span className="font-semibold text-sm text-slate-700 dark:text-slate-300 group-hover:text-slate-900 dark:group-hover:text-white">
                    {section.name}
                  </span>
                  <Badge variant="secondary" className="text-xs">
                    {filteredItems.length}
                  </Badge>
                </div>
                <div className="transition-transform duration-300">
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4 text-slate-500 group-hover:text-blue-600" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-slate-500 group-hover:text-blue-600" />
                  )}
                </div>
              </div>

              {/* Section Items */}
              {isExpanded && (
                <div className="bg-white dark:bg-slate-950">
                  {filteredItems.map((item, index) => {
                    const ItemIcon = item.icon;
                    const isActive = activeSection === item.code;
                    const hasSubItems =
                      item.subItems &&
                      getFilteredSubItems(item.subItems).length > 0;
                    const isItemExpanded = expandedItems.includes(item.code);

                    return (
                      <div key={item.code}>
                        <div
                          className={cn(
                            "flex items-center gap-3 px-6 py-3 cursor-pointer group transition-all duration-200",
                            "hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50",
                            "dark:hover:from-blue-900/10 dark:hover:to-purple-900/10",
                            isActive &&
                              "bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 border-r-2 border-blue-500"
                          )}
                          onClick={() => handleItemClick(item, section)}
                        >
                          <div
                            className={cn(
                              "p-1.5 rounded-md transition-all duration-200",
                              isActive
                                ? "bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-md"
                                : "bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400 group-hover:bg-blue-500 group-hover:text-white"
                            )}
                          >
                            <ItemIcon className="h-3.5 w-3.5" />
                          </div>
                          <span
                            className={cn(
                              "text-sm transition-colors duration-200 flex-1",
                              isActive
                                ? "text-blue-700 dark:text-blue-300 font-medium"
                                : "text-slate-600 dark:text-slate-400 group-hover:text-slate-900 dark:group-hover:text-white"
                            )}
                          >
                            {item.name}
                          </span>
                          {hasSubItems && (
                            <div className="transition-transform duration-200">
                              {isItemExpanded ? (
                                <ChevronDown className="h-3 w-3 text-slate-400" />
                              ) : (
                                <ChevronRight className="h-3 w-3 text-slate-400" />
                              )}
                            </div>
                          )}
                          {isActive && !hasSubItems && (
                            <Sparkles className="h-3 w-3 text-blue-500 animate-pulse" />
                          )}
                        </div>

                        {/* Sub Items */}
                        {hasSubItems && isItemExpanded && (
                          <div className="bg-slate-50 dark:bg-slate-900/50">
                            {getFilteredSubItems(item.subItems).map(
                              (subItem, subIndex) => {
                                const SubItemIcon = subItem.icon;
                                const isSubActive =
                                  activeSection === subItem.code;

                                return (
                                  <div key={subItem.code}>
                                    <div
                                      className={cn(
                                        "flex items-center gap-3 px-10 py-2.5 cursor-pointer group transition-all duration-200",
                                        "hover:bg-gradient-to-r hover:from-emerald-50 hover:to-blue-50",
                                        "dark:hover:from-emerald-900/10 dark:hover:to-blue-900/10",
                                        isSubActive &&
                                          "bg-gradient-to-r from-emerald-100 to-blue-100 dark:from-emerald-900/20 dark:to-blue-900/20 border-r-2 border-emerald-500"
                                      )}
                                      onClick={() =>
                                        handleSubItemClick(
                                          subItem,
                                          item,
                                          section
                                        )
                                      }
                                    >
                                      <div
                                        className={cn(
                                          "p-1 rounded-sm transition-all duration-200",
                                          isSubActive
                                            ? "bg-gradient-to-br from-emerald-500 to-blue-600 text-white shadow-sm"
                                            : "bg-slate-200 dark:bg-slate-700 text-slate-500 dark:text-slate-400 group-hover:bg-emerald-500 group-hover:text-white"
                                        )}
                                      >
                                        <SubItemIcon className="h-3 w-3" />
                                      </div>
                                      <span
                                        className={cn(
                                          "text-xs transition-colors duration-200 flex-1",
                                          isSubActive
                                            ? "text-emerald-700 dark:text-emerald-300 font-medium"
                                            : "text-slate-500 dark:text-slate-400 group-hover:text-slate-800 dark:group-hover:text-slate-200"
                                        )}
                                      >
                                        {subItem.name}
                                      </span>
                                      {isSubActive && (
                                        <Sparkles className="h-2.5 w-2.5 text-emerald-500 animate-pulse" />
                                      )}
                                    </div>
                                    {subIndex <
                                      getFilteredSubItems(item.subItems)
                                        .length -
                                        1 && <Separator className="mx-10" />}
                                  </div>
                                );
                              }
                            )}
                          </div>
                        )}

                        {index < filteredItems.length - 1 && (
                          <Separator className="mx-6" />
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          );
        })}
    </div>
  );

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden lg:flex bg-white dark:bg-slate-950 border-b border-slate-200 dark:border-slate-800">
        {navigationItems
          .filter((section) => hasAccess(section.roles || ["admin", "user"]))
          .map((section) => {
            const Icon = section.icon;
            const filteredItems = getFilteredItems(section.items);

            if (filteredItems.length === 0) return null;

            return (
              <div key={section.code} className="relative group">
                <Button
                  variant="ghost"
                  className={cn(
                    "h-12 px-4 text-sm font-medium rounded-none border-b-2 border-transparent",
                    "hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50",
                    "dark:hover:from-blue-900/20 dark:hover:to-purple-900/20",
                    "hover:border-blue-300 transition-all duration-300"
                  )}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {section.name}
                  <ChevronDown className="h-3 w-3 ml-2 transition-transform group-hover:rotate-180" />
                </Button>

                {/* Dropdown Menu */}
                <div className="absolute top-full left-0 w-80 bg-white dark:bg-slate-950 border border-slate-200 dark:border-slate-800 shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 rounded-lg overflow-hidden">
                  {/* Header */}
                  <div className="bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-3">
                    <div className="flex items-center gap-2 text-white">
                      <Icon className="h-4 w-4" />
                      <span className="font-semibold text-sm">
                        {section.name}
                      </span>
                      <Badge
                        variant="secondary"
                        className="ml-auto bg-white/20 text-white"
                      >
                        {filteredItems.length}
                      </Badge>
                    </div>
                  </div>

                  {/* Items */}
                  <div className="max-h-96 overflow-y-auto">
                    {filteredItems.map((item, index) => {
                      const ItemIcon = item.icon;
                      const isActive = activeSection === item.code;
                      const hasSubItems =
                        item.subItems &&
                        getFilteredSubItems(item.subItems).length > 0;

                      return (
                        <div key={item.code} className="relative group/item">
                          <div
                            className={cn(
                              "flex items-center gap-3 px-4 py-3 cursor-pointer transition-all duration-200",
                              "hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50",
                              "dark:hover:from-blue-900/10 dark:hover:to-purple-900/10",
                              isActive &&
                                "bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20"
                            )}
                            onClick={() => {
                              if (!hasSubItems) {
                                const fullPath = `${
                                  section.pathPrefix || "/danh-muc"
                                }${item.path}`;
                                const menuItem = getMenuItemByPath(fullPath);

                                if (menuItem) {
                                  openTab({
                                    id: fullPath,
                                    title: item.name,
                                    path: fullPath,
                                  });
                                }
                                onSectionChange(item.code);
                              }
                              // Nếu có subItems thì không làm gì, chỉ để hover hiện submenu
                            }}
                          >
                            <div
                              className={cn(
                                "p-2 rounded-lg transition-all duration-200",
                                isActive
                                  ? "bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-md"
                                  : "bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400"
                              )}
                            >
                              <ItemIcon className="h-4 w-4" />
                            </div>
                            <span
                              className={cn(
                                "text-sm transition-colors duration-200 flex-1",
                                isActive
                                  ? "text-blue-700 dark:text-blue-300 font-medium"
                                  : "text-slate-700 dark:text-slate-300"
                              )}
                            >
                              {item.name}
                            </span>
                            {hasSubItems && (
                              <ArrowRight className="h-4 w-4 text-slate-400 group-hover/item:text-blue-500" />
                            )}
                            {isActive && !hasSubItems && (
                              <Sparkles className="h-3 w-3 text-blue-500 animate-pulse" />
                            )}
                          </div>

                          {/* Sub Menu - Hiện bên ngoài dropdown */}
                          {hasSubItems && (
                            <div
                              className="fixed bg-white dark:bg-slate-950 border border-slate-200 dark:border-slate-800 shadow-xl opacity-0 invisible group-hover/item:opacity-100 group-hover/item:visible transition-all duration-300 z-[60] rounded-lg overflow-hidden w-72"
                              style={{
                                left: "100%",
                                top: "0",
                                marginLeft: "4px",
                              }}
                            >
                              {/* Sub Header */}
                              <div className="bg-gradient-to-r from-emerald-500 to-blue-600 px-4 py-2">
                                <div className="flex items-center gap-2 text-white">
                                  <ItemIcon className="h-3.5 w-3.5" />
                                  <span className="font-medium text-sm">
                                    {item.name}
                                  </span>
                                  <Badge
                                    variant="secondary"
                                    className="ml-auto bg-white/20 text-white text-xs"
                                  >
                                    {getFilteredSubItems(item.subItems).length}
                                  </Badge>
                                </div>
                              </div>

                              {/* Sub Items */}
                              <div className="max-h-64 overflow-y-auto">
                                {getFilteredSubItems(item.subItems).map(
                                  (subItem, subIndex) => {
                                    const SubItemIcon = subItem.icon;
                                    const isSubActive =
                                      activeSection === subItem.code;

                                    return (
                                      <div key={subItem.code}>
                                        <div
                                          className={cn(
                                            "flex items-center gap-3 px-4 py-2.5 cursor-pointer transition-all duration-200",
                                            "hover:bg-gradient-to-r hover:from-emerald-50 hover:to-blue-50",
                                            "dark:hover:from-emerald-900/10 dark:hover:to-blue-900/10",
                                            isSubActive &&
                                              "bg-gradient-to-r from-emerald-100 to-blue-100 dark:from-emerald-900/20 dark:to-blue-900/20"
                                          )}
                                          onClick={() => {
                                            const fullPath = `${
                                              section.pathPrefix || "/danh-muc"
                                            }${subItem.path}`;
                                            const menuItem =
                                              getMenuItemByPath(fullPath);

                                            if (menuItem) {
                                              openTab({
                                                id: fullPath,
                                                title: subItem.name,
                                                path: fullPath,
                                              });
                                            }
                                            onSectionChange(subItem.code);
                                          }}
                                        >
                                          <div
                                            className={cn(
                                              "p-1.5 rounded-md transition-all duration-200",
                                              isSubActive
                                                ? "bg-gradient-to-br from-emerald-500 to-blue-600 text-white shadow-sm"
                                                : "bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400"
                                            )}
                                          >
                                            <SubItemIcon className="h-3.5 w-3.5" />
                                          </div>
                                          <span
                                            className={cn(
                                              "text-sm transition-colors duration-200 flex-1",
                                              isSubActive
                                                ? "text-emerald-700 dark:text-emerald-300 font-medium"
                                                : "text-slate-700 dark:text-slate-300"
                                            )}
                                          >
                                            {subItem.name}
                                          </span>
                                          {isSubActive && (
                                            <Sparkles className="h-3 w-3 text-emerald-500 animate-pulse" />
                                          )}
                                        </div>
                                        {subIndex <
                                          getFilteredSubItems(item.subItems)
                                            .length -
                                            1 && <Separator />}
                                      </div>
                                    );
                                  }
                                )}
                              </div>
                            </div>
                          )}

                          {index < filteredItems.length - 1 && <Separator />}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            );
          })}
      </nav>

      {/* Mobile Navigation */}
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button variant="outline" size="icon" className="lg:hidden h-10 w-10">
            <Menu className="h-5 w-5" />
          </Button>
        </SheetTrigger>
        <SheetContent
          side="left"
          className="w-80 p-0 bg-white dark:bg-slate-950"
        >
          <SheetTitle className="sr-only">Menu Navigation</SheetTitle>
          <SheetDescription className="sr-only">
            Hệ thống menu điều hướng cho ứng dụng quản lý nhân sự
          </SheetDescription>

          {/* Mobile Header */}
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4">
            <div className="flex items-center gap-3 text-white">
              <div className="p-2 bg-white/20 rounded-lg">
                <Menu className="h-5 w-5" />
              </div>
              <div>
                <h2 className="font-bold text-sm">HỆ THỐNG QUẢN LÝ NHÂN SỰ</h2>
                <p className="text-xs opacity-90">
                  Vai trò: {userRole.toUpperCase()}
                </p>
              </div>
            </div>
          </div>

          {/* Mobile Content */}
          <div className="h-full overflow-y-auto">
            <NavigationContent />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
