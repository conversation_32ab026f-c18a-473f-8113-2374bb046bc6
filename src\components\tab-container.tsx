"use client";

import { useTabs } from "@/app/contexts/TabsContext";
import { getComponentForTab } from "@/app/lib/tab-registry";
import { cn } from "@/lib/utils";
import { X, Home, Maximize2, Minimize2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import DashboardHomePage from "@/app/(dashboard)/page";
import { useState } from "react";

function TabBar() {
  const { tabs, activeTabId, setActiveTabId, closeTab, closeAllTabs } =
    useTabs();
  const [isMaximized, setIsMaximized] = useState(false);

  if (tabs.length === 0) {
    return null;
  }

  return (
    <div className="flex items-center bg-gradient-to-r from-white to-slate-50 dark:from-slate-900 dark:to-slate-800 border-b border-slate-200 dark:border-slate-700 shadow-sm">
      {/* Tab Navigation */}
      <div className="flex-1 flex items-center overflow-x-auto scrollbar-hide">
        <div className="flex items-center min-w-0">
          {tabs.map((tab, index) => (
            <div
              key={tab.id}
              className={cn(
                "group relative flex items-center min-w-0 max-w-[200px]",
                "border-r border-slate-200 dark:border-slate-700 last:border-r-0"
              )}
            >
              {/* Tab Button */}
              <button
                onClick={() => setActiveTabId(tab.id)}
                className={cn(
                  "flex items-center gap-2 px-4 py-3 min-w-0 transition-all duration-200",
                  "hover:bg-slate-100 dark:hover:bg-slate-800",
                  activeTabId === tab.id
                    ? "bg-white dark:bg-slate-900 text-blue-600 dark:text-blue-400 shadow-sm border-t-2 border-t-blue-500"
                    : "text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200"
                )}
              >
                {/* Tab Icon */}
                <div
                  className={cn(
                    "w-2 h-2 rounded-full transition-colors",
                    activeTabId === tab.id
                      ? "bg-blue-500"
                      : "bg-slate-400 group-hover:bg-slate-500"
                  )}
                />

                {/* Tab Title */}
                <span className="text-sm font-medium truncate">
                  {tab.title}
                </span>

                {/* Active Tab Indicator */}
                {activeTabId === tab.id && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500" />
                )}
              </button>

              {/* Close Button */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className={cn(
                        "h-6 w-6 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200",
                        "hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-600 dark:hover:text-red-400",
                        "absolute right-1 top-1/2 -translate-y-1/2"
                      )}
                      onClick={(e) => {
                        e.stopPropagation();
                        closeTab(tab.id);
                      }}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Đóng tab</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          ))}
        </div>
      </div>

      {/* Tab Controls */}
      <div className="flex items-center gap-2 px-4 py-2 border-l border-slate-200 dark:border-slate-700">
        {/* Tab Count Badge */}
        <Badge variant="secondary" className="text-xs">
          {tabs.length} tab{tabs.length > 1 ? "s" : ""}
        </Badge>

        {/* Maximize/Minimize Button */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7"
                onClick={() => setIsMaximized(!isMaximized)}
              >
                {isMaximized ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{isMaximized ? "Thu nhỏ" : "Phóng to"}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* Close All Tabs Button */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-600 dark:hover:text-red-400"
                onClick={closeAllTabs}
              >
                <Home className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Đóng tất cả và về trang chủ</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
}

function TabContent() {
  const { tabs, activeTabId } = useTabs();

  return (
    <div className="flex-grow bg-white dark:bg-slate-950 overflow-auto">
      {tabs.map((tab) => {
        const Component = getComponentForTab(tab.path);
        const isVisible = tab.id === activeTabId;

        return (
          <div
            key={tab.id}
            className={cn("h-full", isVisible ? "block" : "hidden")}
          >
            <div className="p-6 h-full">
              {Component ? (
                <Component />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="text-slate-400 text-lg mb-2">⚠️</div>
                    <p className="text-slate-600 dark:text-slate-400">
                      Không tìm thấy nội dung trang
                    </p>
                    <p className="text-sm text-slate-500 dark:text-slate-500 mt-1">
                      Path: {tab.path}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        );
      })}

      {tabs.length === 0 && (
        <div className="p-6 h-full">
          <DashboardHomePage />
        </div>
      )}
    </div>
  );
}

export function TabContainer() {
  return (
    <div className="flex flex-col h-full w-full">
      <TabBar />
      <TabContent />
    </div>
  );
}
