"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FileSignature,
  Plus,
  Search,
  Edit,
  Trash2,
  Award,
  Calendar,
  User,
} from "lucide-react";

interface SkillCertificate {
  id: string;
  name: string;
  code: string;
  level: "basic" | "intermediate" | "advanced" | "expert";
  field: string;
  description: string;
  validityPeriod: number; // months
  issuingAuthority: string;
  status: "active" | "inactive";
  employeeCount: number;
}

export default function ChungNhanTayNghePage() {
  const [certificates, setCertificates] = useState<SkillCertificate[]>([
    {
      id: "1",
      name: "Chứng Nhận Hàn Điện",
      code: "CN-HD-001",
      level: "intermediate",
      field: "Kỹ thuật",
      description: "Chứng nhận kỹ năng hàn điện cơ bản và nâng cao",
      validityPeriod: 24,
      issuingAuthority: "Bộ Lao Động",
      status: "active",
      employeeCount: 15,
    },
    {
      id: "2",
      name: "Chứng Nhận Vận Hành Máy",
      code: "CN-VHM-002",
      level: "advanced",
      field: "Vận hành",
      description: "Chứng nhận vận hành máy móc công nghiệp",
      validityPeriod: 36,
      issuingAuthority: "Sở Công Thương",
      status: "active",
      employeeCount: 8,
    },
    {
      id: "3",
      name: "Chứng Nhận An Toàn Lao Động",
      code: "CN-ATLD-003",
      level: "basic",
      field: "An toàn",
      description: "Chứng nhận kiến thức an toàn lao động",
      validityPeriod: 12,
      issuingAuthority: "Thanh Tra Lao Động",
      status: "inactive",
      employeeCount: 0,
    },
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingCertificate, setEditingCertificate] = useState<SkillCertificate | null>(null);

  const filteredCertificates = certificates.filter((cert) =>
    cert.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cert.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getLevelBadge = (level: string) => {
    switch (level) {
      case "basic":
        return <Badge variant="secondary">Cơ bản</Badge>;
      case "intermediate":
        return <Badge variant="default">Trung cấp</Badge>;
      case "advanced":
        return <Badge variant="destructive">Cao cấp</Badge>;
      case "expert":
        return <Badge className="bg-purple-600">Chuyên gia</Badge>;
      default:
        return <Badge variant="outline">Không xác định</Badge>;
    }
  };

  const handleAddCertificate = () => {
    setEditingCertificate(null);
    setIsDialogOpen(true);
  };

  const handleEditCertificate = (certificate: SkillCertificate) => {
    setEditingCertificate(certificate);
    setIsDialogOpen(true);
  };

  const handleDeleteCertificate = (id: string) => {
    setCertificates(certificates.filter((cert) => cert.id !== id));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Chứng Nhận Tay Nghề
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Quản lý các loại chứng nhận tay nghề và kỹ năng chuyên môn
          </p>
        </div>
        <Button onClick={handleAddCertificate} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Thêm Chứng Nhận
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <FileSignature className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Tổng Chứng Nhận
                </p>
                <p className="text-xl font-semibold">{certificates.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <Award className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Đang Hoạt Động
                </p>
                <p className="text-xl font-semibold">
                  {certificates.filter((c) => c.status === "active").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                <User className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Nhân Viên Có CN
                </p>
                <p className="text-xl font-semibold">
                  {certificates.reduce((sum, c) => sum + c.employeeCount, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                <Calendar className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Lĩnh Vực
                </p>
                <p className="text-xl font-semibold">
                  {new Set(certificates.map((c) => c.field)).size}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Tìm kiếm chứng nhận..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Certificates Table */}
      <Card>
        <CardHeader>
          <CardTitle>Danh Sách Chứng Nhận Tay Nghề</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Mã CN</TableHead>
                <TableHead>Tên Chứng Nhận</TableHead>
                <TableHead>Lĩnh Vực</TableHead>
                <TableHead>Cấp Độ</TableHead>
                <TableHead>Hiệu Lực</TableHead>
                <TableHead>Cơ Quan Cấp</TableHead>
                <TableHead>Nhân Viên</TableHead>
                <TableHead>Trạng Thái</TableHead>
                <TableHead>Thao Tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCertificates.map((certificate) => (
                <TableRow key={certificate.id}>
                  <TableCell className="font-medium">{certificate.code}</TableCell>
                  <TableCell>{certificate.name}</TableCell>
                  <TableCell>{certificate.field}</TableCell>
                  <TableCell>{getLevelBadge(certificate.level)}</TableCell>
                  <TableCell>{certificate.validityPeriod} tháng</TableCell>
                  <TableCell>{certificate.issuingAuthority}</TableCell>
                  <TableCell>{certificate.employeeCount} người</TableCell>
                  <TableCell>
                    <Badge
                      variant={certificate.status === "active" ? "default" : "secondary"}
                    >
                      {certificate.status === "active" ? "Hoạt động" : "Tạm dừng"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditCertificate(certificate)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteCertificate(certificate.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingCertificate ? "Chỉnh Sửa Chứng Nhận" : "Thêm Chứng Nhận Mới"}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Tên Chứng Nhận</Label>
              <Input id="name" placeholder="Nhập tên chứng nhận" />
            </div>
            <div>
              <Label htmlFor="code">Mã Chứng Nhận</Label>
              <Input id="code" placeholder="CN-XXX-001" />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="field">Lĩnh Vực</Label>
                <Input id="field" placeholder="Kỹ thuật" />
              </div>
              <div>
                <Label htmlFor="level">Cấp Độ</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn cấp độ" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="basic">Cơ bản</SelectItem>
                    <SelectItem value="intermediate">Trung cấp</SelectItem>
                    <SelectItem value="advanced">Cao cấp</SelectItem>
                    <SelectItem value="expert">Chuyên gia</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="validity">Hiệu Lực (tháng)</Label>
                <Input id="validity" type="number" placeholder="24" />
              </div>
              <div>
                <Label htmlFor="status">Trạng Thái</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn trạng thái" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Hoạt động</SelectItem>
                    <SelectItem value="inactive">Tạm dừng</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label htmlFor="authority">Cơ Quan Cấp</Label>
              <Input id="authority" placeholder="Bộ Lao Động" />
            </div>
            <div>
              <Label htmlFor="description">Mô Tả</Label>
              <Textarea id="description" placeholder="Mô tả chi tiết về chứng nhận" />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Hủy
              </Button>
              <Button onClick={() => setIsDialogOpen(false)}>
                {editingCertificate ? "Cập Nhật" : "Thêm Mới"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
