"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Shield,
  Plus,
  Search,
  Edit,
  Trash2,
  AlertTriangle,
  Calendar,
  User,
  CheckCircle,
} from "lucide-react";

interface SafetyCertificate {
  id: string;
  name: string;
  code: string;
  category: "fire" | "chemical" | "electrical" | "general" | "machinery";
  riskLevel: "low" | "medium" | "high" | "critical";
  description: string;
  validityPeriod: number; // months
  renewalRequired: boolean;
  issuingAuthority: string;
  status: "active" | "inactive" | "expired";
  employeeCount: number;
  lastUpdated: string;
}

export default function ChungNhanAnToanPage() {
  const [certificates, setCertificates] = useState<SafetyCertificate[]>([
    {
      id: "1",
      name: "Chứng Nhận PCCC",
      code: "CN-PCCC-001",
      category: "fire",
      riskLevel: "high",
      description: "Chứng nhận phòng cháy chữa cháy cơ bản",
      validityPeriod: 12,
      renewalRequired: true,
      issuingAuthority: "Cảnh Sát PCCC",
      status: "active",
      employeeCount: 45,
      lastUpdated: "2024-01-15",
    },
    {
      id: "2",
      name: "Chứng Nhận An Toàn Điện",
      code: "CN-ATD-002",
      category: "electrical",
      riskLevel: "critical",
      description: "Chứng nhận an toàn khi làm việc với điện",
      validityPeriod: 24,
      renewalRequired: true,
      issuingAuthority: "Sở Công Thương",
      status: "active",
      employeeCount: 20,
      lastUpdated: "2024-01-10",
    },
    {
      id: "3",
      name: "Chứng Nhận Hóa Chất",
      code: "CN-HC-003",
      category: "chemical",
      riskLevel: "high",
      description: "Chứng nhận an toàn khi làm việc với hóa chất",
      validityPeriod: 18,
      renewalRequired: true,
      issuingAuthority: "Bộ Y Tế",
      status: "expired",
      employeeCount: 0,
      lastUpdated: "2023-12-01",
    },
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingCertificate, setEditingCertificate] = useState<SafetyCertificate | null>(null);

  const filteredCertificates = certificates.filter((cert) =>
    cert.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cert.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getCategoryBadge = (category: string) => {
    switch (category) {
      case "fire":
        return <Badge className="bg-red-600">PCCC</Badge>;
      case "electrical":
        return <Badge className="bg-yellow-600">Điện</Badge>;
      case "chemical":
        return <Badge className="bg-purple-600">Hóa chất</Badge>;
      case "machinery":
        return <Badge className="bg-blue-600">Máy móc</Badge>;
      case "general":
        return <Badge variant="secondary">Chung</Badge>;
      default:
        return <Badge variant="outline">Khác</Badge>;
    }
  };

  const getRiskLevelBadge = (level: string) => {
    switch (level) {
      case "low":
        return <Badge variant="secondary">Thấp</Badge>;
      case "medium":
        return <Badge variant="default">Trung bình</Badge>;
      case "high":
        return <Badge variant="destructive">Cao</Badge>;
      case "critical":
        return <Badge className="bg-red-800">Nghiêm trọng</Badge>;
      default:
        return <Badge variant="outline">Không xác định</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge variant="default">Hoạt động</Badge>;
      case "inactive":
        return <Badge variant="secondary">Tạm dừng</Badge>;
      case "expired":
        return <Badge variant="destructive">Hết hạn</Badge>;
      default:
        return <Badge variant="outline">Không xác định</Badge>;
    }
  };

  const handleAddCertificate = () => {
    setEditingCertificate(null);
    setIsDialogOpen(true);
  };

  const handleEditCertificate = (certificate: SafetyCertificate) => {
    setEditingCertificate(certificate);
    setIsDialogOpen(true);
  };

  const handleDeleteCertificate = (id: string) => {
    setCertificates(certificates.filter((cert) => cert.id !== id));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Chứng Nhận An Toàn
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Quản lý các loại chứng nhận an toàn lao động và bảo hộ
          </p>
        </div>
        <Button onClick={handleAddCertificate} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Thêm Chứng Nhận
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Tổng Chứng Nhận
                </p>
                <p className="text-xl font-semibold">{certificates.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Đang Hoạt Động
                </p>
                <p className="text-xl font-semibold">
                  {certificates.filter((c) => c.status === "active").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Hết Hạn
                </p>
                <p className="text-xl font-semibold">
                  {certificates.filter((c) => c.status === "expired").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                <User className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Nhân Viên Có CN
                </p>
                <p className="text-xl font-semibold">
                  {certificates
                    .filter((c) => c.status === "active")
                    .reduce((sum, c) => sum + c.employeeCount, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Tìm kiếm chứng nhận an toàn..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Certificates Table */}
      <Card>
        <CardHeader>
          <CardTitle>Danh Sách Chứng Nhận An Toàn</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Mã CN</TableHead>
                <TableHead>Tên Chứng Nhận</TableHead>
                <TableHead>Loại</TableHead>
                <TableHead>Mức Độ Rủi Ro</TableHead>
                <TableHead>Hiệu Lực</TableHead>
                <TableHead>Cơ Quan Cấp</TableHead>
                <TableHead>Nhân Viên</TableHead>
                <TableHead>Trạng Thái</TableHead>
                <TableHead>Thao Tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCertificates.map((certificate) => (
                <TableRow key={certificate.id}>
                  <TableCell className="font-medium">{certificate.code}</TableCell>
                  <TableCell>{certificate.name}</TableCell>
                  <TableCell>{getCategoryBadge(certificate.category)}</TableCell>
                  <TableCell>{getRiskLevelBadge(certificate.riskLevel)}</TableCell>
                  <TableCell>{certificate.validityPeriod} tháng</TableCell>
                  <TableCell>{certificate.issuingAuthority}</TableCell>
                  <TableCell>{certificate.employeeCount} người</TableCell>
                  <TableCell>{getStatusBadge(certificate.status)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditCertificate(certificate)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteCertificate(certificate.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingCertificate ? "Chỉnh Sửa Chứng Nhận" : "Thêm Chứng Nhận Mới"}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Tên Chứng Nhận</Label>
              <Input id="name" placeholder="Nhập tên chứng nhận" />
            </div>
            <div>
              <Label htmlFor="code">Mã Chứng Nhận</Label>
              <Input id="code" placeholder="CN-XXX-001" />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">Loại</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn loại" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fire">PCCC</SelectItem>
                    <SelectItem value="electrical">Điện</SelectItem>
                    <SelectItem value="chemical">Hóa chất</SelectItem>
                    <SelectItem value="machinery">Máy móc</SelectItem>
                    <SelectItem value="general">Chung</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="riskLevel">Mức Độ Rủi Ro</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn mức độ" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Thấp</SelectItem>
                    <SelectItem value="medium">Trung bình</SelectItem>
                    <SelectItem value="high">Cao</SelectItem>
                    <SelectItem value="critical">Nghiêm trọng</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="validity">Hiệu Lực (tháng)</Label>
                <Input id="validity" type="number" placeholder="12" />
              </div>
              <div>
                <Label htmlFor="status">Trạng Thái</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn trạng thái" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Hoạt động</SelectItem>
                    <SelectItem value="inactive">Tạm dừng</SelectItem>
                    <SelectItem value="expired">Hết hạn</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label htmlFor="authority">Cơ Quan Cấp</Label>
              <Input id="authority" placeholder="Cảnh Sát PCCC" />
            </div>
            <div>
              <Label htmlFor="description">Mô Tả</Label>
              <Textarea id="description" placeholder="Mô tả chi tiết về chứng nhận" />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Hủy
              </Button>
              <Button onClick={() => setIsDialogOpen(false)}>
                {editingCertificate ? "Cập Nhật" : "Thêm Mới"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
